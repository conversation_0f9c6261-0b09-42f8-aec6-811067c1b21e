DROP FUNCTION IF EXISTS public.tms_ace_get_city_capacity_data_for_7_days(json);

CREATE OR REPLACE FUNCTION public.tms_ace_get_city_capacity_data_for_7_days(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    status bool;
    message text;
    resp_data json;
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    city_name_ text;
    start_date_ date;
    end_date_ date;
    current_date_ date;
    days_data json[];
    single_day_data json;
    day_name text;
    formatted_date text;
    total_slots integer;
    available_slots integer;
    total_capacity integer;
    available_capacity integer;
    day_counter integer;
BEGIN
    status = false;
    message = 'Internal_error';
    resp_data = '[]'::json;

    -- Extract parameters from form_data
    org_id_ = (form_data->>'org_id')::integer;
    usr_id_ = form_data->>'usr_id';
    ip_address_ = form_data->>'ip_address';
    user_agent_ = form_data->>'user_agent';
    city_name_ = form_data->>'city_name';

    -- Validate required parameters
    -- IF org_id_ IS NULL OR city_name_ IS NULL THEN
    --     message = 'Missing required parameters: org_id and city_name';
    --     resp_data = json_build_object(
    --         'status', status,
    --         'message', message,
    --         'data', '[]'::json
    --     );
    --     RETURN resp_data;
    -- END IF;

    -- Set date range for next 7 days starting from today
    start_date_ = CURRENT_DATE;
    end_date_ = CURRENT_DATE + INTERVAL '6 days';
    
    -- Initialize array for days data
    days_data = ARRAY[]::json[];
    day_counter = 0;

    -- Loop through each day for the next 7 days
    FOR current_date_ IN SELECT generate_series(start_date_, end_date_, '1 day'::interval)::date
    LOOP
        day_counter = day_counter + 1;
        
        -- Get day name
        day_name = TO_CHAR(current_date_, 'Day');
        day_name = TRIM(day_name);
        
        -- Format date
        formatted_date = TO_CHAR(current_date_, 'Month DD, YYYY');
        
        -- Get capacity data for this city and date
        SELECT 
            COALESCE(COUNT(DISTINCT cap.resource_id), 0),
            COALESCE(SUM(cap.total_capacity), 0),
            COALESCE(SUM(cap.available_capacity), 0)
        INTO 
            total_slots,
            total_capacity,
            available_capacity
        FROM public.cl_tx_capacity cap
        INNER JOIN public.cl_tx_vertical_srvc_hubs hub 
            ON hub.org_id = 2
            AND hub.is_active = true
          --  AND hub.city = city_name_
            AND cap.resource_id LIKE 2::text || '_' || hub.vertical_id::text || '_%_' || hub.id::text
        WHERE cap.usr_tmzone_day = current_date_;
        
        -- If no data found, set default values
        IF total_slots IS NULL THEN
            total_slots = 0;
        END IF;
        IF total_capacity IS NULL THEN
            total_capacity = 0;
        END IF;
        IF available_capacity IS NULL THEN
            available_capacity = 0;
        END IF;
        
        -- Create single day data object
        single_day_data = json_build_object(
            'id', LOWER(day_name) || '_' || day_counter,
            'name', day_name,
            'date', formatted_date,
            'totalSlots', total_slots,
            'available', available_capacity::text || '/' || total_capacity::text,
            'totalCapacity', total_capacity,
            'availableCapacity', available_capacity,
            'dayDate', current_date_,
            'is_available', CASE WHEN available_capacity > 0 THEN true ELSE false END
        );
        
        -- Add to days array
        days_data = array_append(days_data, single_day_data);
    END LOOP;

    -- Convert array to JSON
    resp_data = array_to_json(days_data);
    
    status = true;
    message = 'success';

    RETURN json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );

EXCEPTION
    WHEN OTHERS THEN
        status = false;
        message = 'Database error: ' || SQLERRM;
        resp_data = '[]'::json;
        
        RETURN json_build_object(
            'status', status,
            'message', message,
            'data', resp_data
        );
END;
$function$;
